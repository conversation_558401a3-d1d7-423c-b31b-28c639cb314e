<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件URL代理功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-title {
            color: #374151;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-output {
            background: #1f2937;
            color: #d1d5db;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .status-success {
            border-left: 4px solid #10b981;
        }
        
        .status-error {
            border-left: 4px solid #ef4444;
        }
        
        .status-warning {
            border-left: 4px solid #f59e0b;
        }
        
        .config-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .config-info h4 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }
        
        .config-info code {
            background: #dbeafe;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 13px;
        }
        
        .proxy-flow {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .proxy-flow h4 {
            margin: 0 0 10px 0;
            color: #16a34a;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        
        .flow-arrow {
            margin: 0 10px;
            color: #16a34a;
            font-weight: bold;
        }
        
        .test-data {
            background: #fefce8;
            border: 1px solid #fde047;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .test-data h4 {
            margin: 0 0 10px 0;
            color: #a16207;
        }
    </style>
</head>
<body>
    <h1>文件URL代理功能测试</h1>
    
    <div class="config-info">
        <h4>代理配置信息</h4>
        <p><strong>代理路径:</strong> <code>/api/file-url</code></p>
        <p><strong>目标服务:</strong> <code>http://192.168.1.225:4001</code></p>
        <p><strong>API端点:</strong> <code>/getFileUrlByCollectionId</code></p>
        <p><strong>配置文件:</strong> vite.config.js</p>
    </div>
    
    <div class="proxy-flow">
        <h4>代理请求流程</h4>
        <div class="flow-step">
            <span>前端请求</span>
            <span class="flow-arrow">→</span>
            <span>/api/file-url/getFileUrlByCollectionId</span>
        </div>
        <div class="flow-step">
            <span>Vite代理</span>
            <span class="flow-arrow">→</span>
            <span>http://192.168.1.225:4001/getFileUrlByCollectionId</span>
        </div>
        <div class="flow-step">
            <span>后台服务</span>
            <span class="flow-arrow">→</span>
            <span>返回文件URL字符串</span>
        </div>
    </div>
    
    <div class="test-data">
        <h4>测试数据</h4>
        <p><strong>CollectionId:</strong> 685ba2186130313d1d0809c5</p>
        <p><strong>用户名:</strong> 苏二川</p>
        <p><strong>工号:</strong> 012652</p>
    </div>
    
    <div class="test-section">
        <div class="test-title">1. 代理配置测试</div>
        <p>测试Vite代理是否正确转发请求到后台服务。</p>
        <button class="test-button" onclick="testProxy()">测试代理配置</button>
        <div id="proxyOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. 文件URL服务测试</div>
        <p>测试封装的文件URL服务是否正常工作。</p>
        <button class="test-button" onclick="testFileUrlService()">测试文件URL服务</button>
        <div id="serviceOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 错误处理测试</div>
        <p>测试各种错误情况的处理。</p>
        <button class="test-button" onclick="testErrorHandling()">测试错误处理</button>
        <div id="errorOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">4. 完整流程测试</div>
        <p>运行完整的测试流程，验证所有功能。</p>
        <button class="test-button" onclick="runCompleteTest()">运行完整测试</button>
        <div id="completeOutput" class="test-output" style="display: none;"></div>
    </div>

    <script>
        // 测试数据
        const testData = {
            collectionId: '685ba2186130313d1d0809c5',
            user: {
                username: '苏二川',
                employeeId: '012652'
            }
        };

        // 模拟用户服务
        class MockUserService {
            constructor() {
                this.currentUser = null;
            }

            async login(userData) {
                this.currentUser = {
                    ...userData,
                    loginTime: new Date().toISOString()
                };
                return true;
            }

            async getCurrentUser() {
                return this.currentUser;
            }

            isLoggedIn() {
                return !!this.currentUser;
            }
        }

        // 模拟文件URL服务
        class MockFileUrlService {
            constructor() {
                this.baseUrl = '/api/file-url';
                this.userService = new MockUserService();
            }

            async getFileUrl(collectionId) {
                if (!collectionId) {
                    throw new Error('collectionId不能为空');
                }

                const currentUser = await this.userService.getCurrentUser();
                if (!currentUser) {
                    throw new Error('用户未登录，无法获取文件链接');
                }

                const params = new URLSearchParams({
                    collectionId: collectionId,
                    username: currentUser.username,
                    jobnumber: currentUser.employeeId
                });

                const response = await fetch(`${this.baseUrl}/getFileUrlByCollectionId?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text().catch(() => '未知错误');
                    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
                }

                const fileUrl = await response.text();
                return fileUrl.trim();
            }

            openFileUrl(fileUrl, newWindow = true) {
                if (!fileUrl) {
                    throw new Error('文件URL为空');
                }

                if (newWindow) {
                    const newTab = window.open(fileUrl, '_blank');
                    if (!newTab) {
                        throw new Error('无法打开新窗口，可能被浏览器阻止');
                    }
                } else {
                    window.location.href = fileUrl;
                }
            }

            getBaseUrl() {
                return this.baseUrl;
            }
        }

        // 创建服务实例
        const fileUrlService = new MockFileUrlService();

        // 测试函数
        window.testProxy = async function() {
            const output = document.getElementById('proxyOutput');
            output.style.display = 'block';
            
            let result = '=== 代理配置测试 ===\n\n';
            let hasError = false;
            
            try {
                // 先登录用户
                await fileUrlService.userService.login(testData.user);
                
                const proxyUrl = '/api/file-url/getFileUrlByCollectionId';
                const params = new URLSearchParams({
                    collectionId: testData.collectionId,
                    username: testData.user.username,
                    jobnumber: testData.user.employeeId
                });
                
                const fullUrl = `${proxyUrl}?${params}`;
                
                result += `请求URL: ${fullUrl}\n`;
                result += `代理目标: http://192.168.1.225:4001/getFileUrlByCollectionId\n\n`;
                
                result += '发送请求...\n';
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                result += `响应状态: ${response.status} ${response.statusText}\n`;
                
                if (response.ok) {
                    const fileUrl = await response.text();
                    result += `✓ 代理请求成功\n`;
                    result += `返回的文件URL: ${fileUrl}\n`;
                } else {
                    const errorText = await response.text().catch(() => '无法读取错误信息');
                    result += `✗ 代理请求失败\n`;
                    result += `错误信息: ${errorText}\n`;
                    hasError = true;
                }
                
            } catch (error) {
                result += `✗ 代理测试异常: ${error.message}\n`;
                hasError = true;
            }
            
            output.textContent = result;
            output.className = `test-output ${hasError ? 'status-error' : 'status-success'}`;
        };

        window.testFileUrlService = async function() {
            const output = document.getElementById('serviceOutput');
            output.style.display = 'block';
            
            let result = '=== 文件URL服务测试 ===\n\n';
            let hasError = false;
            
            try {
                result += `服务基础URL: ${fileUrlService.getBaseUrl()}\n\n`;
                
                // 登录用户
                result += '1. 用户登录...\n';
                await fileUrlService.userService.login(testData.user);
                const currentUser = await fileUrlService.userService.getCurrentUser();
                result += `   用户: ${currentUser.username} (${currentUser.employeeId})\n\n`;
                
                // 获取文件URL
                result += '2. 获取文件URL...\n';
                result += `   CollectionId: ${testData.collectionId}\n`;
                
                const fileUrl = await fileUrlService.getFileUrl(testData.collectionId);
                
                if (fileUrl) {
                    result += `   ✓ 获取成功: ${fileUrl}\n`;
                    
                    // 测试URL格式
                    const urlPattern = /^https?:\/\/.+/;
                    const isValidFormat = urlPattern.test(fileUrl);
                    result += `   URL格式验证: ${isValidFormat ? '✓ 有效' : '✗ 无效'}\n`;
                    
                } else {
                    result += `   ✗ 获取失败\n`;
                    hasError = true;
                }
                
            } catch (error) {
                result += `✗ 服务测试失败: ${error.message}\n`;
                hasError = true;
            }
            
            output.textContent = result;
            output.className = `test-output ${hasError ? 'status-error' : 'status-success'}`;
        };

        window.testErrorHandling = async function() {
            const output = document.getElementById('errorOutput');
            output.style.display = 'block';
            
            let result = '=== 错误处理测试 ===\n\n';
            let allTestsPassed = true;
            
            // 先登录用户
            await fileUrlService.userService.login(testData.user);
            
            const testCases = [
                { name: '空collectionId', collectionId: '' },
                { name: 'null collectionId', collectionId: null },
                { name: 'undefined collectionId', collectionId: undefined },
                { name: '无效collectionId', collectionId: 'invalid-id-123' }
            ];
            
            for (const testCase of testCases) {
                try {
                    result += `测试: ${testCase.name}\n`;
                    const fileUrl = await fileUrlService.getFileUrl(testCase.collectionId);
                    result += `  ✗ 意外成功: ${fileUrl}\n`;
                    allTestsPassed = false;
                } catch (error) {
                    result += `  ✓ 正确捕获错误: ${error.message}\n`;
                }
                result += '\n';
            }
            
            // 测试未登录情况
            result += '测试: 用户未登录\n';
            const originalUser = fileUrlService.userService.currentUser;
            fileUrlService.userService.currentUser = null;
            
            try {
                await fileUrlService.getFileUrl(testData.collectionId);
                result += '  ✗ 意外成功\n';
                allTestsPassed = false;
            } catch (error) {
                result += `  ✓ 正确捕获错误: ${error.message}\n`;
            }
            
            // 恢复用户状态
            fileUrlService.userService.currentUser = originalUser;
            
            result += `\n错误处理测试: ${allTestsPassed ? '✓ 全部通过' : '✗ 部分失败'}\n`;
            
            output.textContent = result;
            output.className = `test-output ${allTestsPassed ? 'status-success' : 'status-error'}`;
        };

        window.runCompleteTest = async function() {
            const output = document.getElementById('completeOutput');
            output.style.display = 'block';
            
            let result = '=== 完整流程测试 ===\n\n';
            let totalTests = 0;
            let passedTests = 0;
            
            // 测试1: 代理配置
            result += '测试1: 代理配置\n';
            totalTests++;
            try {
                await fileUrlService.userService.login(testData.user);
                const params = new URLSearchParams({
                    collectionId: testData.collectionId,
                    username: testData.user.username,
                    jobnumber: testData.user.employeeId
                });
                
                const response = await fetch(`/api/file-url/getFileUrlByCollectionId?${params}`);
                if (response.ok) {
                    result += '  ✓ 通过\n';
                    passedTests++;
                } else {
                    result += `  ✗ 失败: HTTP ${response.status}\n`;
                }
            } catch (error) {
                result += `  ✗ 失败: ${error.message}\n`;
            }
            
            // 测试2: 文件URL服务
            result += '测试2: 文件URL服务\n';
            totalTests++;
            try {
                const fileUrl = await fileUrlService.getFileUrl(testData.collectionId);
                if (fileUrl && fileUrl.length > 0) {
                    result += '  ✓ 通过\n';
                    passedTests++;
                } else {
                    result += '  ✗ 失败: 未获取到文件URL\n';
                }
            } catch (error) {
                result += `  ✗ 失败: ${error.message}\n`;
            }
            
            // 测试3: 错误处理
            result += '测试3: 错误处理\n';
            totalTests++;
            try {
                await fileUrlService.getFileUrl('');
                result += '  ✗ 失败: 应该抛出错误\n';
            } catch (error) {
                result += '  ✓ 通过\n';
                passedTests++;
            }
            
            result += `\n测试总结: ${passedTests}/${totalTests} 通过\n`;
            
            if (passedTests === totalTests) {
                result += '🎉 所有测试通过！代理配置正常工作。\n';
                result += '\n功能说明:\n';
                result += '1. ✓ Vite代理正确转发请求\n';
                result += '2. ✓ 文件URL服务正常工作\n';
                result += '3. ✓ 错误处理机制有效\n';
                result += '4. ✓ 跨域问题已解决\n';
            } else {
                result += '❌ 部分测试失败，需要检查配置。\n';
                result += '\n故障排除:\n';
                result += '1. 检查 vite.config.js 代理配置\n';
                result += '2. 确认后台服务正常运行\n';
                result += '3. 检查网络连接\n';
                result += '4. 验证API端点路径\n';
            }
            
            output.textContent = result;
            output.className = `test-output ${passedTests === totalTests ? 'status-success' : 'status-error'}`;
        };
    </script>
</body>
</html>
