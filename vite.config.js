import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'vue': 'vue/dist/vue.esm-bundler.js'
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['buffer']
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000,      // 与package.json保持一致
    open: true,
    // 配置代理解决CORS问题
    proxy: {
      // AI API代理
      '/api/v1': {
        target: 'http://***********:3006',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('AI API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('AI API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('AI API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // Redis代理服务
      '/redis': {
        target: 'http://***********:4001',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Redis代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Redis代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Redis代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // SSO代理服务
      '/api/sso': {
        target: 'http://***********:4003',  // 使用IPv4地址而不是localhost
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('SSO代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('SSO代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('SSO代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // 日志代理服务
      '/api/logs': {
        target: 'http://***********:4002',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('日志代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('日志代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('日志代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // DOC文件解析API代理
      '/api/doc-parse': {
        target: 'http://***********:4000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/doc-parse/, '/AI/parseDoc'),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('DOC解析API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('DOC解析API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('DOC解析API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // PDF文件解析API代理
      '/api/pdf-parse': {
        target: 'http://***********:4000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/pdf-parse/, '/AI/parsePdf'),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('PDF解析API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('PDF解析API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('PDF解析API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // 文件URL获取API代理
      '/api/file-url': {
        target: 'http://192.168.1.225:4001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/file-url/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('文件URL获取API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('文件URL获取API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('文件URL获取API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
