/**
 * 文件URL获取服务
 * 负责通过collectionId获取文件访问链接
 */

import { getUserService } from './userService.js'

/**
 * 文件URL服务类
 */
class FileUrlService {
  constructor() {
    this.userService = getUserService()
    this.baseUrl = '/api/file-url' // 使用代理路径
  }

  /**
   * 根据collectionId获取文件URL
   * @param {string} collectionId - 集合的唯一标识
   * @returns {Promise<string|null>} 文件URL或null
   */
  async getFileUrl(collectionId) {
    try {
      // 验证参数
      if (!collectionId) {
        throw new Error('collectionId不能为空')
      }

      // 获取当前用户信息
      const currentUser = await this.userService.getCurrentUser()
      if (!currentUser) {
        throw new Error('用户未登录，无法获取文件链接')
      }

      // 验证用户信息
      if (!currentUser.username || !currentUser.employeeId) {
        throw new Error('用户信息不完整，缺少用户名或工号')
      }

      // 构建请求参数
      const params = new URLSearchParams({
        collectionId: collectionId,
        username: currentUser.username,
        jobnumber: currentUser.employeeId
      })

      console.log('获取文件URL请求参数:', {
        collectionId,
        username: currentUser.username,
        jobnumber: currentUser.employeeId
      })

      // 发送请求
      const response = await fetch(`${this.baseUrl}/getFileUrlByCollectionId?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text().catch(() => '未知错误')
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
      }

      // 获取文件URL
      const fileUrl = await response.text()
      const trimmedUrl = fileUrl.trim()

      if (!trimmedUrl) {
        throw new Error('服务器返回空的文件URL')
      }

      console.log('成功获取文件URL:', trimmedUrl)
      return trimmedUrl

    } catch (error) {
      console.error('获取文件URL失败:', error)
      
      // 根据错误类型提供更友好的错误信息
      if (error.message.includes('用户未登录')) {
        throw new Error('请先登录后再尝试获取文件链接')
      } else if (error.message.includes('用户信息不完整')) {
        throw new Error('用户信息异常，请重新登录')
      } else if (error.message.includes('collectionId不能为空')) {
        throw new Error('文件标识缺失，无法获取文件链接')
      } else if (error.message.includes('HTTP 404')) {
        throw new Error('文件不存在或已被删除')
      } else if (error.message.includes('HTTP 403')) {
        throw new Error('没有权限访问该文件')
      } else if (error.message.includes('HTTP 500')) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error(`获取文件链接失败: ${error.message}`)
      }
    }
  }

  /**
   * 批量获取文件URL
   * @param {Array<string>} collectionIds - 集合ID数组
   * @returns {Promise<Object>} 包含成功和失败结果的对象
   */
  async getMultipleFileUrls(collectionIds) {
    if (!Array.isArray(collectionIds) || collectionIds.length === 0) {
      return { success: {}, failed: {} }
    }

    const results = {
      success: {},
      failed: {}
    }

    // 并发请求所有文件URL
    const promises = collectionIds.map(async (collectionId) => {
      try {
        const url = await this.getFileUrl(collectionId)
        results.success[collectionId] = url
      } catch (error) {
        results.failed[collectionId] = error.message
      }
    })

    await Promise.all(promises)

    console.log('批量获取文件URL结果:', {
      成功: Object.keys(results.success).length,
      失败: Object.keys(results.failed).length
    })

    return results
  }

  /**
   * 验证文件URL是否有效
   * @param {string} fileUrl - 文件URL
   * @returns {Promise<boolean>} URL是否有效
   */
  async validateFileUrl(fileUrl) {
    try {
      if (!fileUrl) {
        return false
      }

      // 简单的URL格式验证
      const urlPattern = /^https?:\/\/.+/
      if (!urlPattern.test(fileUrl)) {
        return false
      }

      // 可以添加更多验证逻辑，比如发送HEAD请求检查文件是否存在
      // 这里暂时只做格式验证
      return true

    } catch (error) {
      console.error('验证文件URL失败:', error)
      return false
    }
  }

  /**
   * 打开文件链接
   * @param {string} fileUrl - 文件URL
   * @param {boolean} newWindow - 是否在新窗口打开，默认true
   */
  openFileUrl(fileUrl, newWindow = true) {
    try {
      if (!fileUrl) {
        throw new Error('文件URL为空')
      }

      if (newWindow) {
        const newTab = window.open(fileUrl, '_blank')
        if (!newTab) {
          throw new Error('无法打开新窗口，可能被浏览器阻止')
        }
      } else {
        window.location.href = fileUrl
      }

      console.log('成功打开文件链接:', fileUrl)

    } catch (error) {
      console.error('打开文件链接失败:', error)
      throw error
    }
  }

  /**
   * 获取API基础URL（用于测试和调试）
   * @returns {string} API基础URL
   */
  getBaseUrl() {
    return this.baseUrl
  }

  /**
   * 设置API基础URL（用于测试环境）
   * @param {string} baseUrl - 新的基础URL
   */
  setBaseUrl(baseUrl) {
    this.baseUrl = baseUrl
    console.log('文件URL服务基础URL已更新:', baseUrl)
  }
}

// 创建单例实例
let fileUrlServiceInstance = null

/**
 * 获取文件URL服务实例
 * @returns {FileUrlService} 文件URL服务实例
 */
export function getFileUrlService() {
  if (!fileUrlServiceInstance) {
    fileUrlServiceInstance = new FileUrlService()
  }
  return fileUrlServiceInstance
}

// 导出服务类（用于测试）
export { FileUrlService }

// 默认导出服务实例
export default getFileUrlService()
