/**
 * 文件URL代理功能测试
 * 用于测试代理配置和文件URL服务
 */

import { getFileUrlService } from '../services/fileUrlService.js'
import { getUserService } from '../services/userService.js'

/**
 * 测试数据
 */
const testData = {
  collectionId: '685ba2186130313d1d0809c5',
  user: {
    username: '苏二川',
    employeeId: '012652'
  }
}

/**
 * 测试代理配置
 */
async function testProxyConfiguration() {
  console.log('=== 测试代理配置 ===')
  
  const proxyUrl = '/api/file-url/getFileUrlByCollectionId'
  const params = new URLSearchParams({
    collectionId: testData.collectionId,
    username: testData.user.username,
    jobnumber: testData.user.employeeId
  })
  
  const fullUrl = `${proxyUrl}?${params}`
  
  console.log('代理URL:', fullUrl)
  console.log('原始API:', 'http://192.168.1.225:4001/getFileUrlByCollectionId')
  console.log('代理路径:', '/api/file-url -> http://192.168.1.225:4001')
  
  try {
    console.log('发送代理请求...')
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    console.log('响应状态:', response.status, response.statusText)
    console.log('响应头:', Object.fromEntries(response.headers.entries()))
    
    if (response.ok) {
      const result = await response.text()
      console.log('✓ 代理请求成功')
      console.log('返回结果:', result)
      return { success: true, result }
    } else {
      const errorText = await response.text().catch(() => '无法读取错误信息')
      console.log('✗ 代理请求失败')
      console.log('错误信息:', errorText)
      return { success: false, error: errorText }
    }
    
  } catch (error) {
    console.log('✗ 代理请求异常')
    console.error('异常信息:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试文件URL服务
 */
async function testFileUrlService() {
  console.log('\n=== 测试文件URL服务 ===')
  
  const fileUrlService = getFileUrlService()
  const userService = getUserService()
  
  console.log('服务基础URL:', fileUrlService.getBaseUrl())
  
  try {
    // 模拟用户登录
    console.log('模拟用户登录...')
    await userService.login(testData.user)
    
    const currentUser = await userService.getCurrentUser()
    console.log('当前用户:', currentUser)
    
    if (!currentUser) {
      console.log('✗ 用户未登录，无法测试文件URL服务')
      return { success: false, error: '用户未登录' }
    }
    
    // 测试获取文件URL
    console.log('测试获取文件URL...')
    const fileUrl = await fileUrlService.getFileUrl(testData.collectionId)
    
    if (fileUrl) {
      console.log('✓ 文件URL获取成功')
      console.log('文件URL:', fileUrl)
      
      // 测试URL验证
      const isValid = await fileUrlService.validateFileUrl(fileUrl)
      console.log('URL格式验证:', isValid ? '✓ 有效' : '✗ 无效')
      
      return { success: true, fileUrl, isValid }
    } else {
      console.log('✗ 文件URL获取失败')
      return { success: false, error: '获取文件URL失败' }
    }
    
  } catch (error) {
    console.log('✗ 文件URL服务测试失败')
    console.error('错误信息:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试批量获取文件URL
 */
async function testBatchFileUrls() {
  console.log('\n=== 测试批量获取文件URL ===')
  
  const fileUrlService = getFileUrlService()
  const testCollectionIds = [
    '685ba2186130313d1d0809c5',
    '685ba2186130313d1d0809c6',
    '685ba2186130313d1d0809c7'
  ]
  
  try {
    console.log('测试集合ID:', testCollectionIds)
    const results = await fileUrlService.getMultipleFileUrls(testCollectionIds)
    
    console.log('批量获取结果:')
    console.log('成功:', Object.keys(results.success).length, '个')
    console.log('失败:', Object.keys(results.failed).length, '个')
    
    Object.entries(results.success).forEach(([id, url]) => {
      console.log(`  ✓ ${id}: ${url}`)
    })
    
    Object.entries(results.failed).forEach(([id, error]) => {
      console.log(`  ✗ ${id}: ${error}`)
    })
    
    return results
    
  } catch (error) {
    console.log('✗ 批量获取测试失败')
    console.error('错误信息:', error)
    return { success: {}, failed: {} }
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===')
  
  const fileUrlService = getFileUrlService()
  const testCases = [
    { name: '空collectionId', collectionId: '' },
    { name: 'null collectionId', collectionId: null },
    { name: '无效collectionId', collectionId: 'invalid-id-123' }
  ]
  
  const results = []
  
  for (const testCase of testCases) {
    try {
      console.log(`测试: ${testCase.name}`)
      const result = await fileUrlService.getFileUrl(testCase.collectionId)
      console.log(`  意外成功: ${result}`)
      results.push({ ...testCase, success: true, result })
    } catch (error) {
      console.log(`  ✓ 正确捕获错误: ${error.message}`)
      results.push({ ...testCase, success: false, error: error.message })
    }
  }
  
  return results
}

/**
 * 测试URL打开功能
 */
function testUrlOpening() {
  console.log('\n=== 测试URL打开功能 ===')
  
  const fileUrlService = getFileUrlService()
  const testUrls = [
    'https://www.example.com/test.pdf',
    'http://192.168.1.225:4001/files/test.docx',
    ''
  ]
  
  testUrls.forEach((url, index) => {
    try {
      console.log(`测试URL ${index + 1}: ${url || '(空URL)'}`)
      
      if (!url) {
        fileUrlService.openFileUrl(url)
        console.log('  意外成功')
      } else {
        // 注意：这里不会真正打开URL，只是测试方法调用
        console.log('  ✓ URL格式有效，可以打开')
      }
    } catch (error) {
      console.log(`  ✓ 正确处理错误: ${error.message}`)
    }
  })
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始运行文件URL代理功能测试...\n')
  
  const results = {
    proxy: null,
    service: null,
    batch: null,
    errorHandling: null
  }
  
  try {
    // 测试代理配置
    results.proxy = await testProxyConfiguration()
    
    // 测试文件URL服务
    results.service = await testFileUrlService()
    
    // 测试批量获取
    results.batch = await testBatchFileUrls()
    
    // 测试错误处理
    results.errorHandling = await testErrorHandling()
    
    // 测试URL打开（不会真正打开）
    testUrlOpening()
    
    console.log('\n=== 测试总结 ===')
    console.log('代理配置:', results.proxy?.success ? '✓ 通过' : '✗ 失败')
    console.log('文件URL服务:', results.service?.success ? '✓ 通过' : '✗ 失败')
    console.log('批量获取:', Object.keys(results.batch?.success || {}).length > 0 ? '✓ 部分成功' : '✗ 全部失败')
    console.log('错误处理:', results.errorHandling?.every(r => !r.success) ? '✓ 通过' : '✗ 失败')
    
    const overallSuccess = results.proxy?.success && results.service?.success
    console.log('\n整体测试结果:', overallSuccess ? '🎉 成功' : '❌ 失败')
    
    if (!overallSuccess) {
      console.log('\n故障排除建议:')
      if (!results.proxy?.success) {
        console.log('1. 检查vite.config.js中的代理配置')
        console.log('2. 确认后台服务 http://192.168.1.225:4001 正常运行')
        console.log('3. 检查网络连接')
      }
      if (!results.service?.success) {
        console.log('4. 检查用户登录状态')
        console.log('5. 验证collectionId格式')
        console.log('6. 检查用户权限')
      }
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
  
  console.log('\n测试完成！')
  return results
}

// 导出测试函数
export {
  testProxyConfiguration,
  testFileUrlService,
  testBatchFileUrls,
  testErrorHandling,
  testUrlOpening,
  runAllTests,
  testData
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
  window.fileUrlProxyTest = {
    testProxyConfiguration,
    testFileUrlService,
    testBatchFileUrls,
    testErrorHandling,
    testUrlOpening,
    runAllTests,
    testData
  }
  
  console.log('文件URL代理测试已加载，可以通过 window.fileUrlProxyTest.runAllTests() 运行测试')
}
